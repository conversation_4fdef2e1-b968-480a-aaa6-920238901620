/**
 * Logo Generator Page - Generación y edición de logos con texto usando IA
 * Basado en poster-creator-page.tsx pero adaptado para logos con texto
 */

import React, { useState, useRef, useCallback } from "react";
import { motion } from "framer-motion";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Type,
  Wand2,
  RefreshCw,
  Download,
  Share2,
  Heart,
  HeartOff,
  Upload,
  X,
  Brush,
  Eraser,
  Sparkles,
  ImageIcon,
  Edit3,
  FileImage,
  Trash2,
  Eye,
  EyeOff,
  RotateCcw,
  Settings,
  Layers,
  PaintBucket,
  Zap,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import { useToast } from "@/hooks/use-toast";
import { DashboardLayout } from "@/components/layout/dashboard-layout";
import {
  generateLogoText,
  editWithReferences,
  editWithMask,
  validateImageFile,
  type LogoTextGenerationOptions,
  type LogoTextResponse
} from "@/services/logo-text-service";
import {
  generateLogoWithProgress,
  type LogoGenerationOptions,
  type GenerationState,
  INDUSTRY_OPTIONS,
  COLOR_OPTIONS,
} from "@/services/logo-generator-service";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Progress } from "@/components/ui/progress";

// Tipos para el estado de la aplicación
interface GeneratedLogo {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  response_id?: string;
  metadata?: any;
  timestamp: number;
}

interface SavedLogo {
  id: string;
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
  timestamp: number;
}

// Tipo para el estado de generación (Stability AI)
interface GenerationState {
  isGenerating: boolean;
  progress: number;
  message: string;
}

// Hooks para localStorage
function useLocalStorage<T>(key: string, initialValue: T) {
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  const setValue = (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue] as const;
}

// Utilidades para logos guardados
const SAVED_LOGOS_KEY = "emma-saved-logos";

function createSavedLogo(data: {
  image_url: string;
  prompt: string;
  revised_prompt?: string;
  metadata?: any;
  type: "basic" | "reference" | "mask_edit";
}): SavedLogo {
  return {
    id: `logo-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
    timestamp: Date.now(),
    ...data,
  };
}

function isLogoSaved(imageUrl: string, savedLogos: SavedLogo[]): boolean {
  return savedLogos.some(logo => logo.image_url === imageUrl);
}

export default function LogoGeneratorPage() {
  // Estado para seleccionar el modo de generación
  const [currentMode, setCurrentMode] = useState<"stability" | "openai">("openai");

  // Estados para el modo OpenAI (Logo con Texto)
  const [currentLogo, setCurrentLogo] = useState<GeneratedLogo | null>(null);
  const [prompt, setPrompt] = useState("");
  const [size, setSize] = useState<"1024x1024">("1024x1024"); // Solo cuadrado
  const [isGenerating, setIsGenerating] = useState(false);

  // Estados para el modo Stability AI (Generador Original)
  const [logoDescription, setLogoDescription] = useState("");
  const [industry, setIndustry] = useState("");
  const [colorPreference, setColorPreference] = useState("");
  const [aspectRatio, setAspectRatio] = useState<"1:1" | "16:9" | "9:16">("1:1");
  const [outputFormat, setOutputFormat] = useState<"png" | "jpeg" | "webp">("png");
  const [generationState, setGenerationState] = useState<GenerationState>({
    isGenerating: false,
    progress: 0,
    message: "",
  });
  const [generatedLogo, setGeneratedLogo] = useState<string | null>(null);

  // Estados para edición con referencias
  const [referenceImages, setReferenceImages] = useState<File[]>([]);
  const [referencePrompt, setReferencePrompt] = useState("");

  // Estados para edición con máscara
  const [editingImage, setEditingImage] = useState<string | null>(null);
  const [maskPrompt, setMaskPrompt] = useState("");
  const [isDrawing, setIsDrawing] = useState(false);
  const [brushSize, setBrushSize] = useState(20);
  const [showMask, setShowMask] = useState(true);

  // Estados para favoritos
  const [savedLogos, setSavedLogos] = useLocalStorage<SavedLogo[]>(SAVED_LOGOS_KEY, []);
  const [currentLogoSaved, setCurrentLogoSaved] = useState(false);
  const [mainTab, setMainTab] = useState<"latest" | "saved">("latest");

  const { toast } = useToast();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const maskCanvasRef = useRef<HTMLCanvasElement>(null);
  const imageInputRef = useRef<HTMLInputElement>(null);

  // Prompts de ejemplo para logos con texto
  const examplePrompts = [
    "Logo moderno para empresa de tecnología con el texto 'TechCorp' en tipografía futurista",
    "Logo elegante para cafetería con el texto 'Coffee House' y elementos de granos de café",
    "Logo minimalista para estudio de diseño con el texto 'Creative Studio' en estilo clean",
    "Logo profesional para consultoría con el texto 'Business Pro' y elementos corporativos",
    "Logo vintage para barbería con el texto 'Classic Cuts' y estética retro",
    "Logo deportivo para gimnasio con el texto 'Fit Zone' y elementos dinámicos"
  ];

  // Solo tamaño cuadrado para logos
  const sizeOptions = [
    { value: "1024x1024", label: "Cuadrado (1024x1024)", icon: "⬜" }
  ];

  // Función para generar logo con texto (OpenAI)
  const handleGenerate = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Prompt requerido",
        description: "Por favor, describe el logo con texto que quieres crear",
        variant: "destructive",
      });
      return;
    }

    setIsGenerating(true);

    try {
      const options: LogoTextGenerationOptions = {
        prompt,
        size,
      };

      const result = await generateLogoText(options);

      if (result.success && result.image_url) {
        const newLogo: GeneratedLogo = {
          id: Date.now().toString(),
          image_url: result.image_url,
          prompt,
          revised_prompt: result.revised_prompt,
          response_id: result.response_id,
          metadata: result.metadata,
          timestamp: Date.now(),
        };

        setCurrentLogo(newLogo);

        toast({
          title: "¡Logo generado!",
          description: "Tu logo con texto ha sido creado exitosamente",
        });
      } else {
        throw new Error(result.error || "Error desconocido");
      }
    } catch (error) {
      console.error("Error generating logo:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Error al generar el logo",
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  // Función para generar logo tradicional (Stability AI)
  const handleGenerateStability = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validaciones
    if (!logoDescription.trim()) {
      toast({
        title: "Campo requerido",
        description: "Por favor describe el logo que quieres generar.",
        variant: "destructive",
      });
      return;
    }



    if (!colorPreference.trim()) {
      toast({
        title: "Campo requerido",
        description: "Por favor selecciona una preferencia de color.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Iniciar generación
      setGenerationState({
        isGenerating: true,
        progress: 0,
        message: "Iniciando generación de logo...",
      });

      // Preparar opciones
      const options: LogoGenerationOptions = {
        brandName: logoDescription,
        industry,
        colorPreference,
        aspectRatio,
        outputFormat,
      };

      // Función de callback para progreso
      const progressCallback = (progress: number, message?: string) => {
        setGenerationState({
          isGenerating: true,
          progress,
          message: message || `Progreso: ${progress}%`,
        });
      };

      // Generar logo
      const logoUrl = await generateLogoWithProgress(options, progressCallback);

      // Actualizar estado con resultado
      setGeneratedLogo(logoUrl);
      setGenerationState({
        isGenerating: false,
        progress: 100,
        message: "Logo generado exitosamente",
      });

      toast({
        title: "¡Logo generado!",
        description: "Tu logo ha sido creado exitosamente.",
      });

    } catch (error) {
      console.error("Error generando logo:", error);

      setGenerationState({
        isGenerating: false,
        progress: 0,
        message: "",
      });

      toast({
        title: "Error al generar logo",
        description: error instanceof Error ? error.message : "Error desconocido",
        variant: "destructive",
      });
    }
  };

  // Función para cargar imagen para editar
  const handleLoadImageForEdit = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageUrl = e.target?.result as string;
      setEditingImage(imageUrl);

      // Cargar imagen en el canvas
      setTimeout(() => {
        const canvas = canvasRef.current;
        const maskCanvas = maskCanvasRef.current;
        if (canvas && maskCanvas) {
          const ctx = canvas.getContext('2d');
          const maskCtx = maskCanvas.getContext('2d');
          const img = new Image();

          img.onload = () => {
            // Calcular tamaño apropiado para el canvas (máximo 600px de ancho)
            const maxWidth = 600;
            const maxHeight = 400;
            let { width, height } = img;

            if (width > maxWidth) {
              height = (height * maxWidth) / width;
              width = maxWidth;
            }

            if (height > maxHeight) {
              width = (width * maxHeight) / height;
              height = maxHeight;
            }

            // Configurar tamaño del canvas
            canvas.width = width;
            canvas.height = height;
            maskCanvas.width = width;
            maskCanvas.height = height;

            // Aplicar tamaño CSS para que se vea correctamente
            canvas.style.width = `${width}px`;
            canvas.style.height = `${height}px`;
            maskCanvas.style.width = `${width}px`;
            maskCanvas.style.height = `${height}px`;

            // Dibujar imagen original escalada
            ctx?.drawImage(img, 0, 0, width, height);

            // Limpiar máscara (fondo negro)
            if (maskCtx) {
              maskCtx.fillStyle = 'black';
              maskCtx.fillRect(0, 0, width, height);
            }
          };

          img.src = imageUrl;
        }
      }, 100);
    };
    reader.readAsDataURL(file);
  };

  // Funciones para dibujar en el canvas
  const startDrawing = (e: React.MouseEvent<HTMLCanvasElement>) => {
    setIsDrawing(true);
    draw(e);
  };

  const stopDrawing = () => {
    setIsDrawing(false);
  };

  const draw = (e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing) return;

    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const rect = canvas.getBoundingClientRect();

    // Calcular la escala entre el canvas real y el canvas mostrado
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    // Ajustar coordenadas según la escala
    const x = (e.clientX - rect.left) * scaleX;
    const y = (e.clientY - rect.top) * scaleY;

    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = 'white';
    ctx.beginPath();
    ctx.arc(x, y, (brushSize / 2) * scaleX, 0, 2 * Math.PI);
    ctx.fill();
  };

  // Función para limpiar máscara
  const clearMask = () => {
    const canvas = maskCanvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    ctx.fillStyle = 'black';
    ctx.fillRect(0, 0, canvas.width, canvas.height);
  };

  // Función para manejar archivos de referencia
  const handleReferenceFiles = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach((file) => {
      const validation = validateImageFile(file);
      if (validation.valid) {
        validFiles.push(file);
      } else {
        errors.push(`${file.name}: ${validation.error}`);
      }
    });

    if (errors.length > 0) {
      toast({
        title: "Archivos inválidos",
        description: errors.join(", "),
        variant: "destructive",
      });
    }

    if (validFiles.length > 0) {
      setReferenceImages(prev => [...prev, ...validFiles].slice(0, 4)); // Máximo 4 imágenes
    }
  };

  return (
    <DashboardLayout pageTitle="Generador de Logos">
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50/30 p-6">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="relative bg-gradient-to-r from-purple-600 via-blue-600 to-indigo-700 rounded-2xl p-8 mb-8 overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20"></div>
            <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-br from-white/10 to-transparent rounded-full -translate-y-32 translate-x-32"></div>

            <div className="relative z-10">
              <div className="flex items-center gap-4 mb-4">
                <div className="p-3 bg-white/20 backdrop-blur-sm rounded-xl">
                  <Type className="h-8 w-8 text-white" />
                </div>
                <h1 className="text-4xl font-bold text-white">
                  Generador de Logos
                </h1>
              </div>
              <p className="text-xl text-purple-100 mb-6 max-w-3xl">
                Crea logos profesionales con IA. Elige entre generación tradicional o logos con texto integrado.
              </p>
              <div className="flex flex-wrap gap-2">
                {currentMode === "openai" ? (
                  <>

                    <Badge className="bg-white/20 text-white border-white/30">
                      <Brush className="w-3 h-3 mr-1" />
                      Editor integrado
                    </Badge>
                    <Badge className="bg-white/20 text-white border-white/30">
                      <ImageIcon className="w-3 h-3 mr-1" />
                      Referencias visuales
                    </Badge>
                    <Badge className="bg-white/20 text-white border-white/30">
                      <Edit3 className="w-3 h-3 mr-1" />
                      Edición precisa
                    </Badge>
                  </>
                ) : (
                  <>

                    <Badge className="bg-white/20 text-white border-white/30">
                      <Settings className="w-3 h-3 mr-1" />
                      Configuración avanzada
                    </Badge>
                    <Badge className="bg-white/20 text-white border-white/30">
                      <PaintBucket className="w-3 h-3 mr-1" />
                      Múltiples formatos
                    </Badge>
                  </>
                )}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Mode Selector */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <Tabs value={currentMode} onValueChange={(value) => setCurrentMode(value as "stability" | "openai")} className="w-full">
            <TabsList className="grid w-full grid-cols-2 max-w-2xl mx-auto">
              <TabsTrigger value="stability" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Generador Tradicional
              </TabsTrigger>
              <TabsTrigger value="openai" className="flex items-center gap-2">
                <Type className="h-4 w-4" />
                Logos con Texto
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </motion.div>

        {/* Contenido principal */}
        {currentMode === "stability" && (
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
            {/* Panel de configuración */}
            <div className="lg:col-span-5">
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Configuración del Logo
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleGenerateStability} className="space-y-6">
                    {/* Descripción del logo */}
                    <div className="space-y-2">
                      <Label htmlFor="logoDescription">Descripción del Logo</Label>
                      <Textarea
                        id="logoDescription"
                        placeholder="Describe el logo que quieres crear..."
                        value={logoDescription}
                        onChange={(e) => setLogoDescription(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>



                    {/* Preferencia de color */}
                    <div className="space-y-2">
                      <Label htmlFor="colorPreference">Preferencia de Color</Label>
                      <Select value={colorPreference} onValueChange={setColorPreference}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecciona colores" />
                        </SelectTrigger>
                        <SelectContent>
                          {COLOR_OPTIONS.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>



                    {/* Formato de salida */}
                    <div className="space-y-2">
                      <Label htmlFor="outputFormat">Formato de Salida</Label>
                      <Select value={outputFormat} onValueChange={(value: "png" | "jpeg" | "webp") => setOutputFormat(value)}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="png">PNG (Imagen)</SelectItem>
                          <SelectItem value="jpeg">JPEG (Comprimido)</SelectItem>
                          <SelectItem value="webp">WebP (Moderno)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Botón de generación */}
                    <Button
                      type="submit"
                      disabled={generationState.isGenerating}
                      className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                    >
                      {generationState.isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Sparkles className="h-4 w-4 mr-2" />
                          Generar Logo
                        </>
                      )}
                    </Button>

                    {/* Progreso */}
                    {generationState.isGenerating && (
                      <div className="space-y-2">
                        <Progress value={generationState.progress} className="w-full" />
                        <p className="text-sm text-gray-600 text-center">
                          {generationState.message}
                        </p>
                      </div>
                    )}
                  </form>
                </CardContent>
              </Card>
            </div>

            {/* Panel de resultado */}
            <div className="lg:col-span-7">
              <Card className="border-0 shadow-lg bg-white/80 backdrop-blur-sm">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="flex items-center gap-2">
                      <Eye className="h-5 w-5" />
                      Resultado
                    </CardTitle>
                    {generatedLogo && (
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Heart className="h-4 w-4 mr-1" />
                          Guardar
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={async () => {
                            try {
                              const response = await fetch(generatedLogo);
                              const blob = await response.blob();
                              const url = window.URL.createObjectURL(blob);
                              const a = document.createElement("a");
                              a.href = url;
                              a.download = `logo-${Date.now()}.${outputFormat}`;
                              document.body.appendChild(a);
                              a.click();
                              window.URL.revokeObjectURL(url);
                              document.body.removeChild(a);

                              toast({
                                title: "Descarga iniciada",
                                description: "El logo se está descargando",
                              });
                            } catch (error) {
                              toast({
                                title: "Error",
                                description: "No se pudo descargar la imagen",
                                variant: "destructive",
                              });
                            }
                          }}
                        >
                          <Download className="h-4 w-4 mr-1" />
                          Descargar
                        </Button>
                        <Button size="sm" variant="outline">
                          <Share2 className="h-4 w-4 mr-1" />
                          Compartir
                        </Button>
                      </div>
                    )}
                  </div>
                </CardHeader>
                <CardContent>
                  {generatedLogo ? (
                    <div className="space-y-4">
                      <div className="relative group">
                        <img
                          src={generatedLogo}
                          alt="Logo generado"
                          className="w-full rounded-lg shadow-lg"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg" />
                      </div>
                      <div className="flex flex-wrap gap-2">
                        <Badge variant="secondary">
                          Formato: {outputFormat.toUpperCase()}
                        </Badge>
                        <Badge variant="secondary">
                          Aspecto: {aspectRatio}
                        </Badge>
                        <Badge variant="secondary">
                          Industria: {INDUSTRY_OPTIONS.find(opt => opt.value === industry)?.label || industry}
                        </Badge>
                      </div>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                      <Settings className="h-16 w-16 mb-4 opacity-50" />
                      <h3 className="text-lg font-medium mb-2">No hay logo generado</h3>
                      <p className="text-sm text-center max-w-md">
                        Completa la configuración y haz clic en "Generar Logo" para crear tu logo personalizado
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Modo OpenAI - Generación de Logos con Texto */}
        {currentMode === "openai" && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Panel de Control */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.1 }}
            className="lg:col-span-1"
          >
            <Card className="sticky top-6 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
              <CardHeader className="pb-4">
                <CardTitle className="flex items-center gap-2 text-xl">
                  <Settings className="h-5 w-5 text-purple-600" />
                  Panel de Control
                </CardTitle>
                <CardDescription>
                  Configura y genera tu logo perfecto
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="generate" className="w-full">
                  <TabsList className="grid w-full grid-cols-3 mb-6">
                    <TabsTrigger value="generate" className="text-xs">
                      <Wand2 className="h-3 w-3 mr-1" />
                      Generar
                    </TabsTrigger>
                    <TabsTrigger value="references" className="text-xs">
                      <ImageIcon className="h-3 w-3 mr-1" />
                      Referencias
                    </TabsTrigger>
                    <TabsTrigger value="edit" className="text-xs">
                      <Edit3 className="h-3 w-3 mr-1" />
                      Editar
                    </TabsTrigger>
                  </TabsList>

                  {/* Tab: Generar */}
                  <TabsContent value="generate" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Logo a Generar</Label>
                      <Textarea
                        placeholder="Describe el logo con texto que quieres crear..."
                        value={prompt}
                        onChange={(e) => setPrompt(e.target.value)}
                        className="min-h-[100px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">
                        Tamaño del Logo
                        <span className="ml-2 text-xs text-gray-500">
                          (Solo Cuadrado 1024x1024)
                        </span>
                      </Label>
                      <div className="grid grid-cols-1 gap-2">
                        <Button
                          variant="default"
                          size="sm"
                          disabled
                          className="justify-start bg-purple-600 hover:bg-purple-700 text-white shadow-md"
                        >
                          <span className="mr-2 text-lg">⬜</span>
                          <div className="text-left">
                            <div className="font-medium">Cuadrado</div>
                            <div className="text-xs opacity-75">1024x1024</div>
                          </div>
                          <div className="ml-auto">
                            <div className="w-2 h-2 bg-white rounded-full"></div>
                          </div>
                        </Button>
                      </div>
                    </div>

                    <Button
                      onClick={handleGenerate}
                      disabled={isGenerating || !prompt.trim()}
                      className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    >
                      {isGenerating ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Generando...
                        </>
                      ) : (
                        <>
                          <Wand2 className="h-4 w-4 mr-2" />
                          Generar Logo
                        </>
                      )}
                    </Button>

                    {/* Prompts de ejemplo */}
                    <div className="space-y-2">
                      <Label className="text-xs text-gray-600">Ejemplos:</Label>
                      <div className="space-y-1">
                        {examplePrompts.slice(0, 3).map((example, index) => (
                          <Button
                            key={index}
                            variant="ghost"
                            size="sm"
                            onClick={() => setPrompt(example)}
                            className="w-full text-left text-xs h-auto p-2 justify-start"
                          >
                            {example}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </TabsContent>

                  {/* Tab: Referencias */}
                  <TabsContent value="references" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Descripción del Logo</Label>
                      <Textarea
                        placeholder="Describe el logo que quieres crear basado en las referencias..."
                        value={referencePrompt}
                        onChange={(e) => setReferencePrompt(e.target.value)}
                        className="min-h-[80px] resize-none"
                      />
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Imágenes de Referencia (máx. 4)</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={fileInputRef}
                          type="file"
                          multiple
                          accept="image/*"
                          onChange={(e) => handleReferenceFiles(e.target.files)}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Subir Imágenes
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB cada una
                        </p>
                      </div>

                      {/* Mostrar imágenes de referencia */}
                      {referenceImages.length > 0 && (
                        <div className="grid grid-cols-2 gap-2">
                          {referenceImages.map((file, index) => (
                            <div key={index} className="relative group">
                              <img
                                src={URL.createObjectURL(file)}
                                alt={`Referencia ${index + 1}`}
                                className="w-full h-20 object-cover rounded-lg"
                              />
                              <Button
                                variant="destructive"
                                size="sm"
                                className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                                onClick={() => {
                                  setReferenceImages(prev => prev.filter((_, i) => i !== index));
                                }}
                              >
                                <X className="h-3 w-3" />
                              </Button>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  {/* Tab: Editar */}
                  <TabsContent value="edit" className="space-y-4">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Cargar Imagen para Editar</Label>
                      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                        <input
                          ref={imageInputRef}
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              const validation = validateImageFile(file);
                              if (validation.valid) {
                                handleLoadImageForEdit(file);
                              } else {
                                toast({
                                  title: "Archivo inválido",
                                  description: validation.error,
                                  variant: "destructive",
                                });
                              }
                            }
                          }}
                          className="hidden"
                        />
                        <Button
                          variant="outline"
                          onClick={() => imageInputRef.current?.click()}
                          className="mb-2"
                        >
                          <Upload className="h-4 w-4 mr-2" />
                          Cargar Imagen
                        </Button>
                        <p className="text-xs text-gray-500">
                          PNG, JPG, WebP hasta 10MB
                        </p>
                      </div>
                    </div>

                    {editingImage && (
                      <>
                        <div className="space-y-3">
                          <Label className="text-sm font-medium">Qué quieres cambiar</Label>
                          <Textarea
                            placeholder="Describe qué quieres cambiar en las áreas marcadas..."
                            value={maskPrompt}
                            onChange={(e) => setMaskPrompt(e.target.value)}
                            className="min-h-[80px] resize-none"
                          />
                        </div>

                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <Label className="text-sm font-medium">Herramientas de Edición</Label>
                            <div className="flex items-center gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowMask(!showMask)}
                              >
                                {showMask ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
                              </Button>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={clearMask}
                              >
                                <RotateCcw className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <Label className="text-xs text-gray-600">Tamaño del pincel: {brushSize}px</Label>
                            <Slider
                              value={[brushSize]}
                              onValueChange={(value) => setBrushSize(value[0])}
                              max={50}
                              min={5}
                              step={5}
                              className="w-full"
                            />
                          </div>
                        </div>
                      </>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </motion.div>

          {/* Área de Visualización */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
            className="lg:col-span-2"
          >
            <Tabs value={mainTab} onValueChange={(value) => setMainTab(value as "latest" | "saved")} className="w-full">
              <TabsList className="grid w-full grid-cols-2 mb-6">
                <TabsTrigger value="latest" className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Última Generación
                </TabsTrigger>
                <TabsTrigger value="saved" className="flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Guardados ({savedLogos.length})
                </TabsTrigger>
              </TabsList>

              <TabsContent value="latest" className="space-y-6">
                {/* Logo generado */}
                {currentLogo && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <div className="flex items-center justify-between">
                        <CardTitle className="flex items-center gap-2">
                          <Type className="h-5 w-5 text-purple-600" />
                          Logo Generado
                        </CardTitle>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              if (isLogoSaved(currentLogo.image_url, savedLogos)) {
                                setSavedLogos(prev => prev.filter(logo => logo.image_url !== currentLogo.image_url));
                                setCurrentLogoSaved(false);
                                toast({
                                  title: "Removido de favoritos",
                                  description: "El logo ha sido removido de tus favoritos",
                                });
                              } else {
                                const savedLogo = createSavedLogo({
                                  image_url: currentLogo.image_url,
                                  prompt: currentLogo.prompt,
                                  revised_prompt: currentLogo.revised_prompt,
                                  metadata: currentLogo.metadata,
                                  type: "basic",
                                });
                                setSavedLogos(prev => [savedLogo, ...prev]);
                                setCurrentLogoSaved(true);
                                toast({
                                  title: "Agregado a favoritos",
                                  description: "El logo ha sido guardado en tus favoritos",
                                });
                              }
                            }}
                            className={isLogoSaved(currentLogo.image_url, savedLogos) ? "text-red-600 border-red-200" : ""}
                          >
                            {isLogoSaved(currentLogo.image_url, savedLogos) ? (
                              <Heart className="h-4 w-4 fill-current" />
                            ) : (
                              <HeartOff className="h-4 w-4" />
                            )}
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              try {
                                const response = await fetch(currentLogo.image_url);
                                const blob = await response.blob();
                                const url = window.URL.createObjectURL(blob);
                                const a = document.createElement("a");
                                a.href = url;
                                a.download = `logo-${Date.now()}.png`;
                                document.body.appendChild(a);
                                a.click();
                                window.URL.revokeObjectURL(url);
                                document.body.removeChild(a);

                                toast({
                                  title: "Descarga iniciada",
                                  description: "El logo se está descargando",
                                });
                              } catch (error) {
                                toast({
                                  title: "Error",
                                  description: "No se pudo descargar la imagen",
                                  variant: "destructive",
                                });
                              }
                            }}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Descargar
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={async () => {
                              try {
                                await navigator.clipboard.writeText(currentLogo.image_url);
                                toast({
                                  title: "Enlace copiado",
                                  description: "El enlace del logo se copió al portapapeles",
                                });
                              } catch (error) {
                                toast({
                                  title: "Error",
                                  description: "No se pudo copiar el enlace",
                                  variant: "destructive",
                                });
                              }
                            }}
                          >
                            <Share2 className="h-4 w-4 mr-1" />
                            Compartir
                          </Button>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="relative group">
                        <img
                          src={currentLogo.image_url}
                          alt="Logo generado"
                          className="w-full rounded-lg shadow-lg"
                        />
                        <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors rounded-lg"></div>
                      </div>

                      {/* Información del logo */}
                      <div className="mt-4 space-y-2">
                        <div className="flex items-start gap-2">
                          <Badge variant="secondary" className="mt-0.5">Prompt</Badge>
                          <p className="text-sm text-gray-600 flex-1">{currentLogo.prompt}</p>
                        </div>

                        {currentLogo.revised_prompt && (
                          <div className="flex items-start gap-2">
                            <Badge variant="outline" className="mt-0.5">Revisado</Badge>
                            <p className="text-xs text-gray-500 flex-1">{currentLogo.revised_prompt}</p>
                          </div>
                        )}

                        {currentLogo.metadata && (
                          <div className="flex items-center gap-2 text-xs text-gray-400">
                            <span>Modelo: OpenAI</span>
                            {currentLogo.metadata.size && (
                              <span>• Tamaño: {currentLogo.metadata.size}</span>
                            )}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Editor de Máscara */}
                {editingImage && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="pb-4">
                      <CardTitle className="flex items-center gap-2">
                        <Brush className="h-5 w-5 text-purple-600" />
                        Editor de Máscara
                      </CardTitle>
                      <CardDescription>
                        Pinta las áreas que quieres cambiar en blanco
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="relative flex justify-center">
                        <div className="relative">
                          <canvas
                            ref={canvasRef}
                            className="rounded-lg border border-gray-200"
                            style={{ zIndex: 1, display: 'block' }}
                          />
                          <canvas
                            ref={maskCanvasRef}
                            className={`absolute inset-0 rounded-lg cursor-crosshair ${
                              showMask ? 'opacity-50' : 'opacity-0'
                            }`}
                            style={{
                              zIndex: 2,
                              mixBlendMode: 'multiply',
                              transition: 'opacity 0.2s ease'
                            }}
                            onMouseDown={startDrawing}
                            onMouseMove={draw}
                            onMouseUp={stopDrawing}
                            onMouseLeave={stopDrawing}
                          />
                        </div>
                      </div>

                      <div className="mt-4 text-xs text-gray-500 space-y-1">
                        <p>• Pinta en blanco las áreas que quieres cambiar</p>
                        <p>• Usa el control deslizante para ajustar el tamaño del pincel</p>
                        <p>• Puedes ocultar/mostrar la máscara con el botón del ojo</p>
                      </div>
                    </CardContent>
                  </Card>
                )}

                {/* Estado vacío */}
                {!currentLogo && !editingImage && (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Type className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        ¡Crea tu primer logo con texto!
                      </h3>
                      <p className="text-gray-600 text-center max-w-md mb-6">
                        Describe el logo que quieres crear, usa imágenes de referencia o carga una imagen para editar.
                      </p>
                      <div className="flex flex-wrap gap-2 justify-center">
                        <Badge variant="secondary">Corporativo</Badge>
                        <Badge variant="secondary">Moderno</Badge>
                        <Badge variant="secondary">Minimalista</Badge>
                        <Badge variant="secondary">Profesional</Badge>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="saved" className="space-y-6">
                {savedLogos.length === 0 ? (
                  <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardContent className="flex flex-col items-center justify-center py-16">
                      <div className="w-24 h-24 bg-gradient-to-br from-purple-100 to-blue-100 rounded-full flex items-center justify-center mb-6">
                        <Heart className="h-12 w-12 text-purple-600" />
                      </div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No tienes logos guardados
                      </h3>
                      <p className="text-gray-600 text-center max-w-md">
                        Los logos que marques como favoritos aparecerán aquí para acceso rápido.
                      </p>
                    </CardContent>
                  </Card>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {savedLogos.map((savedLogo) => (
                      <Card key={savedLogo.id} className="shadow-xl border-0 bg-white/80 backdrop-blur-sm overflow-hidden">
                        <div className="relative group">
                          <img
                            src={savedLogo.image_url}
                            alt="Logo guardado"
                            className="w-full h-48 object-cover"
                          />
                          <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors"></div>
                        </div>

                        <CardContent className="p-4">
                          <div className="space-y-3">
                            <div className="flex items-start gap-2">
                              <Badge variant="secondary" className="mt-0.5 text-xs">
                                {savedLogo.type === "basic" ? "Básico" :
                                 savedLogo.type === "reference" ? "Referencias" : "Editado"}
                              </Badge>
                              <p className="text-sm text-gray-600 flex-1 line-clamp-2">
                                {savedLogo.prompt}
                              </p>
                            </div>

                            <div className="text-xs text-gray-400">
                              {new Date(savedLogo.timestamp).toLocaleDateString('es-ES', {
                                year: 'numeric',
                                month: 'short',
                                day: 'numeric',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </div>

                            {/* Botones de acción */}
                            <div className="flex gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                className="flex-1"
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                Ver
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Descargar
                              </Button>
                              <Button
                                size="sm"
                                variant="destructive"
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </TabsContent>
            </Tabs>
          </motion.div>
        </div>
        )}
      </div>
    </DashboardLayout>
  );
}
