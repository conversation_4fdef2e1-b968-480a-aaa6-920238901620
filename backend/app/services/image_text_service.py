"""
Service for image generation using OpenAI's gpt-image-1 model.
Handles image generation, multi-turn editing, and mask-based editing.
"""

import logging
import asyncio
import uuid
import base64
import os
import tempfile
from datetime import datetime
from typing import List, Dict, Any
from fastapi import UploadFile
import aiohttp

from app.models.image_text_models import (
    ServiceImageTextResponse,
    ImageTextGenerationOptions,
    ImageTextMetadata,
    ImageTextError
)
from app.core.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImageTextService:
    """Service for OpenAI gpt-image-1 operations."""
    
    def __init__(self):
        self.api_key = settings.OPENAI_API_KEY
        self.base_url = "https://api.openai.com/v1"
        self.model = "gpt-image-1"
        
        # Session storage for multi-turn editing
        self.session_storage: Dict[str, Dict[str, Any]] = {}
    
    async def generate_image(
        self,
        prompt: str
    ) -> ServiceImageTextResponse:
        """Generate an image using OpenAI's gpt-image-1 model."""
        
        start_time = datetime.now()
        request_id = str(uuid.uuid4())
        
        try:
            logger.info(f"🎨 Generating image with gpt-image-1: {prompt[:100]}...")
            
            # Prepare the request payload for gpt-image-1
            payload = {
                "model": self.model,
                "prompt": prompt
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make the API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/images/generations",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"OpenAI API error: {response.status} - {error_text}")
                        return ServiceImageTextResponse(
                            success=False,
                            error=f"OpenAI API error: {response.status} - {error_text}",
                            request_id=request_id
                        )
                    
                    result = await response.json()
            
            # Extract the image data - handle multiple response formats like logo service
            if "data" in result and len(result["data"]) > 0:
                image_data = result["data"][0]

                # Try to get image in different formats (like logo service)
                b64_image = image_data.get("b64_json") or image_data.get("b64") or image_data.get("image")
                image_url_direct = image_data.get("url")

                if b64_image:
                    # Save image base64 as temporary file (like logo service)
                    image_bytes = base64.b64decode(b64_image)
                    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".png")
                    temp_file.write(image_bytes)
                    temp_file.close()

                    # Return temp file path (like logo service)
                    image_url = f"/temp/{os.path.basename(temp_file.name)}"

                    # Generate response ID for multi-turn editing
                    response_id = str(uuid.uuid4())

                    # Store session data for multi-turn editing
                    self.session_storage[response_id] = {
                        "original_prompt": prompt,
                        "image_b64": b64_image,
                        "timestamp": datetime.now(),
                        "request_id": request_id
                    }

                    generation_time = (datetime.now() - start_time).total_seconds()

                    # Create metadata
                    metadata = ImageTextMetadata(
                        model=self.model,
                        generation_time=generation_time,
                        request_id=request_id
                    )

                    logger.info(f"✅ Image generated successfully in {generation_time:.2f}s")

                    return ServiceImageTextResponse(
                        success=True,
                        image_url=image_url,
                        response_id=response_id,
                        metadata=metadata.model_dump(),
                        generation_time=generation_time,
                        model_used=self.model,
                        request_id=request_id,
                        timestamp=datetime.now()
                    )
                elif image_url_direct:
                    # Use direct URL if available
                    response_id = str(uuid.uuid4())

                    # Store session data for multi-turn editing
                    self.session_storage[response_id] = {
                        "original_prompt": prompt,
                        "image_url": image_url_direct,
                        "timestamp": datetime.now(),
                        "request_id": request_id
                    }

                    generation_time = (datetime.now() - start_time).total_seconds()

                    logger.info(f"✅ Image generated successfully in {generation_time:.2f}s")

                    return ServiceImageTextResponse(
                        success=True,
                        image_url=image_url_direct,
                        response_id=response_id,
                        metadata={
                            "model": self.model,
                            "generation_time": generation_time,
                            "request_id": request_id
                        },
                        generation_time=generation_time,
                        model_used=self.model,
                        request_id=request_id,
                        timestamp=datetime.now()
                    )
                else:
                    logger.error("No image data found in response")
                    return ServiceImageTextResponse(
                        success=False,
                        error="No image data in response",
                        request_id=request_id
                    )
            else:
                logger.error(f"Unexpected response format: {result}")
                return ServiceImageTextResponse(
                    success=False,
                    error="Unexpected response format",
                    request_id=request_id
                )
                
        except asyncio.TimeoutError:
            logger.error("Timeout while generating image with OpenAI")
            return ServiceImageTextResponse(
                success=False,
                error="Request timeout - image generation took too long",
                request_id=request_id
            )
        except Exception as e:
            logger.error(f"Error generating image with OpenAI: {e}", exc_info=True)
            return ServiceImageTextResponse(
                success=False,
                error=f"Internal error during image generation: {str(e)}",
                request_id=request_id
            )
    
    async def edit_with_references(
        self,
        prompt: str,
        reference_images: List[UploadFile]
    ) -> ServiceImageTextResponse:
        """Generate image using reference images with gpt-4.1 responses API."""
        
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"🖼️ Generating image with {len(reference_images)} references using responses API")
            
            # Prepare content array
            content = [{"type": "input_text", "text": prompt}]
            
            # Process reference images
            for i, ref_image in enumerate(reference_images):
                image_data = await ref_image.read()
                
                # Encode to base64
                image_b64 = base64.b64encode(image_data).decode('utf-8')
                
                # Add to content
                content.append({
                    "type": "input_image",
                    "image_url": f"data:image/jpeg;base64,{image_b64}"
                })
            
            # Prepare the request payload for responses API
            payload = {
                "model": "gpt-4.1-mini",
                "input": [
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                "tools": [{"type": "image_generation"}]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make the API call to responses endpoint
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/responses",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"OpenAI responses API error: {response.status} - {error_text}")
                        return ServiceImageTextResponse(
                            success=False,
                            error=f"OpenAI responses API error: {response.status} - {error_text}",
                            request_id=request_id
                        )
                    
                    result = await response.json()
            
            # Extract image generation calls
            image_generation_calls = [
                output for output in result.get("output", [])
                if output.get("type") == "image_generation_call"
            ]
            
            if image_generation_calls:
                image_b64 = image_generation_calls[0].get("result")
                
                if image_b64:
                    image_url = f"data:image/png;base64,{image_b64}"
                    
                    # Generate response ID for multi-turn editing
                    response_id = str(uuid.uuid4())
                    
                    # Store session data including the image generation call ID
                    self.session_storage[response_id] = {
                        "original_prompt": prompt,
                        "image_b64": image_b64,
                        "image_generation_call_id": image_generation_calls[0].get("id"),
                        "timestamp": datetime.now(),
                        "request_id": request_id
                    }
                    
                    generation_time = (datetime.now() - start_time).total_seconds()
                    
                    logger.info(f"✅ Image with references generated successfully in {generation_time:.2f}s")
                    
                    return ServiceImageTextResponse(
                        success=True,
                        image_url=image_url,
                        response_id=response_id,
                        generation_time=generation_time,
                        model_used="gpt-4.1-mini",
                        request_id=request_id,
                        timestamp=datetime.now()
                    )
            
            logger.error("No image data in OpenAI responses")
            return ServiceImageTextResponse(
                success=False,
                error="No image data received from OpenAI responses API",
                request_id=request_id
            )
                
        except Exception as e:
            logger.error(f"Error in reference-based generation: {e}", exc_info=True)
            return ServiceImageTextResponse(
                success=False,
                error=f"Error in reference-based generation: {str(e)}",
                request_id=request_id
            )
    
    async def edit_with_mask(
        self,
        prompt: str,
        image: UploadFile,
        mask: UploadFile
    ) -> ServiceImageTextResponse:
        """Edit image using mask with gpt-image-1."""
        
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"🎭 Editing image with mask using gpt-image-1: {prompt[:100]}...")
            
            # Read image and mask data
            image_data = await image.read()
            mask_data = await mask.read()
            
            # Prepare the multipart form data
            form_data = aiohttp.FormData()
            form_data.add_field('model', self.model)
            form_data.add_field('prompt', prompt)
            form_data.add_field('image', image_data, filename='image.png', content_type='image/png')
            form_data.add_field('mask', mask_data, filename='mask.png', content_type='image/png')
            
            headers = {
                "Authorization": f"Bearer {self.api_key}"
            }
            
            # Make the API call to OpenAI's image editing endpoint
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/images/edits",
                    data=form_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"OpenAI edit API error: {response.status} - {error_text}")
                        return ServiceImageTextResponse(
                            success=False,
                            error=f"OpenAI edit API error: {response.status} - {error_text}",
                            request_id=request_id
                        )
                    
                    result = await response.json()
            
            # Extract the edited image
            if "data" in result and len(result["data"]) > 0:
                image_b64 = result["data"][0]["b64_json"]
                image_url = f"data:image/png;base64,{image_b64}"
                
                generation_time = (datetime.now() - start_time).total_seconds()
                
                logger.info(f"✅ Image edited successfully in {generation_time:.2f}s")
                
                return ServiceImageTextResponse(
                    success=True,
                    image_url=image_url,
                    generation_time=generation_time,
                    model_used=self.model,
                    request_id=request_id,
                    timestamp=datetime.now()
                )
            else:
                logger.error("No image data in OpenAI edit response")
                return ServiceImageTextResponse(
                    success=False,
                    error="No image data received from OpenAI edit",
                    request_id=request_id
                )
                
        except Exception as e:
            logger.error(f"Error editing image with mask: {e}", exc_info=True)
            return ServiceImageTextResponse(
                success=False,
                error=f"Error editing image with mask: {str(e)}",
                request_id=request_id
            )
    
    async def multi_turn_edit(
        self,
        previous_response_id: str,
        edit_prompt: str
    ) -> ServiceImageTextResponse:
        """Perform multi-turn editing using gpt-4.1-mini responses API."""
        
        request_id = str(uuid.uuid4())
        start_time = datetime.now()
        
        try:
            logger.info(f"🔄 Multi-turn editing: {edit_prompt[:100]}...")
            
            # Retrieve previous session data
            if previous_response_id not in self.session_storage:
                return ServiceImageTextResponse(
                    success=False,
                    error="Previous response ID not found. Cannot perform multi-turn editing."
                )
            
            previous_data = self.session_storage[previous_response_id]
            previous_call_id = previous_data.get('image_generation_call_id')
            
            if not previous_call_id:
                # Fallback to simple generation if no call ID
                return await self.generate_image(edit_prompt)
            
            # Prepare the request payload for multi-turn editing
            payload = {
                "model": "gpt-4.1-mini",
                "input": [
                    {
                        "role": "user",
                        "content": [{"type": "input_text", "text": edit_prompt}]
                    },
                    {
                        "type": "image_generation_call",
                        "id": previous_call_id
                    }
                ],
                "tools": [{"type": "image_generation"}]
            }
            
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            # Make the API call
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.base_url}/responses",
                    json=payload,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=60)
                ) as response:
                    
                    if response.status != 200:
                        error_text = await response.text()
                        logger.error(f"OpenAI multi-turn API error: {response.status} - {error_text}")
                        return ServiceImageTextResponse(
                            success=False,
                            error=f"OpenAI multi-turn API error: {response.status} - {error_text}",
                            request_id=request_id
                        )
                    
                    result = await response.json()
            
            # Extract image generation calls
            image_generation_calls = [
                output for output in result.get("output", [])
                if output.get("type") == "image_generation_call"
            ]
            
            if image_generation_calls:
                image_b64 = image_generation_calls[0].get("result")
                
                if image_b64:
                    image_url = f"data:image/png;base64,{image_b64}"
                    
                    # Generate new response ID
                    new_response_id = str(uuid.uuid4())
                    
                    # Store new session data
                    self.session_storage[new_response_id] = {
                        "original_prompt": previous_data['original_prompt'],
                        "edit_prompt": edit_prompt,
                        "image_b64": image_b64,
                        "image_generation_call_id": image_generation_calls[0].get("id"),
                        "previous_response_id": previous_response_id,
                        "timestamp": datetime.now(),
                        "request_id": request_id
                    }
                    
                    generation_time = (datetime.now() - start_time).total_seconds()
                    
                    logger.info(f"✅ Multi-turn edit completed successfully in {generation_time:.2f}s")
                    
                    return ServiceImageTextResponse(
                        success=True,
                        image_url=image_url,
                        response_id=new_response_id,
                        generation_time=generation_time,
                        model_used="gpt-4.1-mini",
                        request_id=request_id,
                        timestamp=datetime.now()
                    )
            
            logger.error("No image data in multi-turn response")
            return ServiceImageTextResponse(
                success=False,
                error="No image data received from multi-turn editing",
                request_id=request_id
            )
            
        except Exception as e:
            logger.error(f"Error in multi-turn editing: {e}", exc_info=True)
            return ServiceImageTextResponse(
                success=False,
                error=f"Error in multi-turn editing: {str(e)}",
                request_id=request_id
            )


# Global service instance
image_text_service = ImageTextService()
